/*
 * Copyright (c) 2012-2024. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.sencha.tools.compiler.ast.js.*;
import com.sencha.tools.compiler.sources.SourceFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * BasicConverter - A comprehensive JavaScript parser that directly converts JavaScript source code
 * into Sencha's rich custom AST nodes without using Google Closure Compiler.
 *
 * This is a proper recursive descent parser that handles comprehensive JavaScript constructs and
 * builds the custom AST tree using the full range of available nodes: RootNode, Block, FunctionNode,
 * ObjectLiteral, ArrayLiteral, ConditionalExpression, etc.
 */
public class BasicConverter {

    private String _source;
    private int _position;
    private int _line;
    private int _column;
    private SourceFile _sourceFile;

    // Token types for proper tokenization
    private enum TokenType {
        IDENTIFIER, NUMBER, STRING, KEYWORD, OPERATOR, PUNCTUATION, WHITESPACE, COMMENT, EOF
    }

    private static class Token {
        TokenType type;
        String value;
        int position;
        int line;
        int column;

        Token(TokenType type, String value, int position, int line, int column) {
            this.type = type;
            this.value = value;
            this.position = position;
            this.line = line;
            this.column = column;
        }
    }

    // JavaScript keywords
    private static final Set<String> KEYWORDS = Set.of(
        "break", "case", "catch", "class", "const", "continue", "debugger", "default",
        "delete", "do", "else", "export", "extends", "finally", "for", "function",
        "if", "import", "in", "instanceof", "let", "new", "return", "super", "switch",
        "this", "throw", "try", "typeof", "var", "void", "while", "with", "yield",
        "async", "await", "of", "static", "get", "set"
    );

    // Current token
    private Token _currentToken;
    private List<Token> _tokens;
    
    public BasicConverter() {
        this._position = 0;
        this._line = 1;
        this._column = 1;
    }

    /**
     * Parse JavaScript source code into Sencha's custom AST
     * @param source The JavaScript source code to parse
     * @param sourceFile The source file information
     * @return RootNode containing the parsed AST
     */
    public RootNode parse(String source, SourceFile sourceFile) {
        this._source = source;
        this._sourceFile = sourceFile;
        this._position = 0;
        this._line = 1;
        this._column = 1;

        // Tokenize the source
        tokenize();

        RootNode root = new RootNode();
        root.setSourceFile(sourceFile);

        // Parse top-level statements
        while (!isAtEndOfTokens()) {
            BaseNode statement = parseStatement();
            if (statement != null) {
                root.addElement(statement);
                setNodePosition(statement);
            }
        }

        return root;
    }

    /**
     * Tokenize the source code into a list of tokens
     */
    private void tokenize() {
        _tokens = new ArrayList<>();
        _position = 0;
        _line = 1;
        _column = 1;

        while (_position < _source.length()) {
            char c = _source.charAt(_position);

            // Skip whitespace
            if (Character.isWhitespace(c)) {
                skipWhitespace();
                continue;
            }

            // Comments
            if (c == '/' && peek(1) == '/') {
                skipLineComment();
                continue;
            }
            if (c == '/' && peek(1) == '*') {
                skipBlockComment();
                continue;
            }

            // String literals
            if (c == '"' || c == '\'' || c == '`') {
                tokenizeString(c);
                continue;
            }

            // Numbers
            if (Character.isDigit(c)) {
                tokenizeNumber();
                continue;
            }

            // Identifiers and keywords
            if (Character.isLetter(c) || c == '_' || c == '$') {
                tokenizeIdentifier();
                continue;
            }

            // Operators and punctuation
            tokenizeOperatorOrPunctuation();
        }

        // Add EOF token
        _tokens.add(new Token(TokenType.EOF, "", _position, _line, _column));
        _currentToken = _tokens.get(0);
    }
    
    // ========== TOKENIZER HELPER METHODS ==========

    private char peek(int offset) {
        int pos = _position + offset;
        return pos < _source.length() ? _source.charAt(pos) : '\0';
    }

    private void skipWhitespace() {
        while (_position < _source.length() && Character.isWhitespace(_source.charAt(_position))) {
            if (_source.charAt(_position) == '\n') {
                _line++;
                _column = 1;
            } else {
                _column++;
            }
            _position++;
        }
    }

    private void skipLineComment() {
        while (_position < _source.length() && _source.charAt(_position) != '\n') {
            _position++;
            _column++;
        }
    }

    private void skipBlockComment() {
        _position += 2; // Skip /*
        _column += 2;
        while (_position < _source.length() - 1) {
            if (_source.charAt(_position) == '*' && _source.charAt(_position + 1) == '/') {
                _position += 2;
                _column += 2;
                break;
            }
            if (_source.charAt(_position) == '\n') {
                _line++;
                _column = 1;
            } else {
                _column++;
            }
            _position++;
        }
    }

    private void tokenizeString(char quote) {
        int start = _position;
        _position++; // Skip opening quote
        _column++;

        StringBuilder value = new StringBuilder();
        while (_position < _source.length() && _source.charAt(_position) != quote) {
            char c = _source.charAt(_position);
            if (c == '\\' && _position + 1 < _source.length()) {
                _position++; // Skip backslash
                _column++;
                char escaped = _source.charAt(_position);
                switch (escaped) {
                    case 'n': value.append('\n'); break;
                    case 't': value.append('\t'); break;
                    case 'r': value.append('\r'); break;
                    case '\\': value.append('\\'); break;
                    case '"': value.append('"'); break;
                    case '\'': value.append('\''); break;
                    default: value.append(escaped); break;
                }
            } else {
                value.append(c);
            }
            if (c == '\n') {
                _line++;
                _column = 1;
            } else {
                _column++;
            }
            _position++;
        }

        if (_position < _source.length()) {
            _position++; // Skip closing quote
            _column++;
        }

        _tokens.add(new Token(TokenType.STRING, value.toString(), start, _line, _column));
    }

    private void tokenizeNumber() {
        int start = _position;
        StringBuilder value = new StringBuilder();

        while (_position < _source.length() &&
               (Character.isDigit(_source.charAt(_position)) || _source.charAt(_position) == '.')) {
            value.append(_source.charAt(_position));
            _position++;
            _column++;
        }

        _tokens.add(new Token(TokenType.NUMBER, value.toString(), start, _line, _column));
    }

    private void tokenizeIdentifier() {
        int start = _position;
        StringBuilder value = new StringBuilder();

        while (_position < _source.length() &&
               (Character.isLetterOrDigit(_source.charAt(_position)) ||
                _source.charAt(_position) == '_' || _source.charAt(_position) == '$')) {
            value.append(_source.charAt(_position));
            _position++;
            _column++;
        }

        String identifier = value.toString();
        TokenType type = KEYWORDS.contains(identifier) ? TokenType.KEYWORD : TokenType.IDENTIFIER;
        _tokens.add(new Token(type, identifier, start, _line, _column));
    }

    private void tokenizeOperatorOrPunctuation() {
        int start = _position;
        char c = _source.charAt(_position);

        // Multi-character operators
        if (_position + 1 < _source.length()) {
            String twoChar = _source.substring(_position, _position + 2);
            switch (twoChar) {
                case "==", "!=", "<=", ">=", "&&", "||", "++", "--", "+=", "-=", "*=", "/=", "=>":
                    _tokens.add(new Token(TokenType.OPERATOR, twoChar, start, _line, _column));
                    _position += 2;
                    _column += 2;
                    return;
            }
        }

        // Single character operators and punctuation
        TokenType type = switch (c) {
            case '+', '-', '*', '/', '%', '=', '<', '>', '!', '&', '|', '^', '~', '?' -> TokenType.OPERATOR;
            case '(', ')', '{', '}', '[', ']', ';', ',', '.', ':' -> TokenType.PUNCTUATION;
            default -> TokenType.OPERATOR; // fallback
        };

        _tokens.add(new Token(type, String.valueOf(c), start, _line, _column));
        _position++;
        _column++;
    }

    // ========== PARSER METHODS ==========

    private boolean isAtEndOfTokens() {
        return _currentToken.type == TokenType.EOF;
    }

    private void setNodePosition(BaseNode node) {
        if (node != null && _currentToken != null) {
            node.setLine(_currentToken.line);
            node.setSourceFile(_sourceFile);
        }
    }

    private boolean matchToken(TokenType type) {
        return _currentToken.type == type;
    }

    private boolean matchKeyword(String keyword) {
        return _currentToken.type == TokenType.KEYWORD && keyword.equals(_currentToken.value);
    }

    private boolean matchPunctuation(String punct) {
        return _currentToken.type == TokenType.PUNCTUATION && punct.equals(_currentToken.value);
    }

    private void nextToken() {
        int currentIndex = _tokens.indexOf(_currentToken);
        if (currentIndex + 1 < _tokens.size()) {
            _currentToken = _tokens.get(currentIndex + 1);
        }
    }

    private boolean consumeKeyword(String keyword) {
        if (matchKeyword(keyword)) {
            nextToken();
            return true;
        }
        return false;
    }

    private boolean consumePunctuation(String punct) {
        if (matchPunctuation(punct)) {
            nextToken();
            return true;
        }
        return false;
    }
    
    /**
     * Parse a single statement using proper token-based parsing
     */
    private BaseNode parseStatement() {
        if (isAtEndOfTokens()) {
            return null;
        }

        // Function declaration
        if (matchKeyword("function")) {
            return parseFunctionDeclaration();
        }

        // Variable declaration
        if (matchKeyword("var") || matchKeyword("let") || matchKeyword("const")) {
            return parseVariableDeclaration();
        }

        // If statement
        if (matchKeyword("if")) {
            return parseIfStatement();
        }

        // For loop
        if (matchKeyword("for")) {
            return parseForStatement();
        }

        // While loop
        if (matchKeyword("while")) {
            return parseWhileStatement();
        }

        // Return statement
        if (matchKeyword("return")) {
            return parseReturnStatement();
        }

        // Block statement
        if (matchPunctuation("{")) {
            return parseBlock();
        }

        // Try statement
        if (matchKeyword("try")) {
            return parseTryStatement();
        }

        // Throw statement
        if (matchKeyword("throw")) {
            return parseThrowStatement();
        }

        // Break statement
        if (matchKeyword("break")) {
            return parseBreakStatement();
        }

        // Continue statement
        if (matchKeyword("continue")) {
            return parseContinueStatement();
        }

        // Default to expression statement
        return parseExpressionStatement();
    }

    /**
     * Parse function declaration: function name() { ... }
     */
    private FunctionNode parseFunctionDeclaration() {
        nextToken(); // consume 'function'

        FunctionNode func = new FunctionNode();
        setNodePosition(func);

        // Parse function name
        if (matchToken(TokenType.IDENTIFIER)) {
            Name nameNode = new Name(_currentToken.value);
            func.setName(nameNode);
            nextToken();
        }

        // Parse parameters
        if (consumePunctuation("(")) {
            parseFunctionParameters(func);
            consumePunctuation(")");
        }

        // Parse function body
        BaseNode body = parseBlock();
        func.setBody(body);

        return func;
    }

    /**
     * Parse function parameters
     */
    private void parseFunctionParameters(FunctionNode func) {
        while (!matchPunctuation(")") && !isAtEndOfTokens()) {
            if (matchToken(TokenType.IDENTIFIER)) {
                Name param = new Name(_currentToken.value);
                setNodePosition(param);
                func.addParam(param);
                nextToken();

                if (matchPunctuation(",")) {
                    nextToken();
                }
            } else {
                break;
            }
        }
    }
    
    /**
     * Parse variable declaration: var x = value;
     */
    private VariableDeclaration parseVariableDeclaration() {
        String keyword = null;
        if (peek("var")) keyword = "var";
        else if (peek("let")) keyword = "let";
        else if (peek("const")) keyword = "const";
        
        consume(keyword);
        skipWhitespaceAndComments();
        
        VariableDeclaration varDecl = new VariableDeclaration();
        varDecl.setSourceFile(_sourceFile);
        varDecl.setLine(_line);
        
        // Parse variable name
        String varName = parseIdentifier();
        if (varName != null) {
            VariableInitializer init = new VariableInitializer();
            Name nameNode = new Name();
            nameNode.setValue(varName);
            init.setTarget(nameNode);
            
            skipWhitespaceAndComments();
            
            // Check for initializer
            if (consume("=")) {
                skipWhitespaceAndComments();
                BaseNode value = parseExpression();
                init.setInitializer(value);
            }
            
            varDecl.addVariable(init);
        }
        
        consume(";");
        return varDecl;
    }
    
    /**
     * Parse if statement: if (condition) { ... }
     */
    private IfStatement parseIfStatement() {
        consume("if");
        skipWhitespaceAndComments();
        
        IfStatement ifStmt = new IfStatement();
        ifStmt.setSourceFile(_sourceFile);
        ifStmt.setLine(_line);
        
        // Parse condition
        if (consume("(")) {
            BaseNode condition = parseExpression();
            ifStmt.setCondition(condition);
            consume(")");
        }
        
        skipWhitespaceAndComments();
        
        // Parse then statement
        BaseNode thenStmt = parseStatement();
        ifStmt.setThen(thenStmt);

        // Check for else
        skipWhitespaceAndComments();
        if (peek("else")) {
            consume("else");
            skipWhitespaceAndComments();
            BaseNode elseStmt = parseStatement();
            ifStmt.setElse(elseStmt);
        }
        
        return ifStmt;
    }
    
    /**
     * Parse block statement: { ... }
     */
    private Block parseBlock() {
        consume("{");
        skipWhitespaceAndComments();
        
        Block block = new Block();
        block.setSourceFile(_sourceFile);
        block.setLine(_line);
        
        while (!peek("}") && !isAtEnd()) {
            BaseNode statement = parseStatement();
            if (statement != null) {
                block.addElement(statement);
            }
            skipWhitespaceAndComments();
        }
        
        consume("}");
        return block;
    }
    
    /**
     * Parse return statement: return expression;
     */
    private ReturnStatement parseReturnStatement() {
        consume("return");
        skipWhitespaceAndComments();
        
        ReturnStatement returnStmt = new ReturnStatement();
        returnStmt.setSourceFile(_sourceFile);
        returnStmt.setLine(_line);
        
        // Check if there's an expression to return
        if (!peek(";") && !isAtEnd()) {
            BaseNode expr = parseExpression();
            returnStmt.setReturnValue(expr);
        }
        
        consume(";");
        return returnStmt;
    }
    
    /**
     * Parse expression statement: expression;
     */
    private ExpressionStatement parseExpressionStatement() {
        BaseNode expr = parseExpression();
        consume(";");
        
        ExpressionStatement exprStmt = new ExpressionStatement();
        exprStmt.setSourceFile(_sourceFile);
        exprStmt.setLine(_line);
        exprStmt.setExpression(expr);
        
        return exprStmt;
    }
    
    /**
     * Parse expression (simplified - handles basic cases)
     */
    private BaseNode parseExpression() {
        return parseAssignmentExpression();
    }
    
    /**
     * Parse assignment expression: left = right
     */
    private BaseNode parseAssignmentExpression() {
        BaseNode left = parseLogicalExpression();
        
        skipWhitespaceAndComments();
        if (peek("=")) {
            consume("=");
            skipWhitespaceAndComments();
            BaseNode right = parseAssignmentExpression();
            
            Assignment assignment = new Assignment();
            assignment.setLeft(left);
            assignment.setRight(right);
            assignment.setOperator(Operators.ASSIGN);
            return assignment;
        }
        
        return left;
    }
    
    /**
     * Parse logical expression (simplified)
     */
    private BaseNode parseLogicalExpression() {
        return parsePrimaryExpression();
    }
    
    /**
     * Parse primary expression: identifiers, literals, etc.
     */
    private BaseNode parsePrimaryExpression() {
        skipWhitespaceAndComments();
        
        // String literal
        if (matchToken(TokenType.STRING)) {
            String value = _currentToken.value;
            nextToken();
            StringLiteral literal = new StringLiteral();
            literal.setValue(value); // Value is already unquoted from tokenizer
            return literal;
        }

        // Number literal
        if (matchToken(TokenType.NUMBER)) {
            String value = _currentToken.value;
            nextToken();
            NumberLiteral literal = new NumberLiteral();
            literal.setValue(Double.parseDouble(value));
            return literal;
        }
        
        // Identifier
        if (matchToken(TokenType.IDENTIFIER)) {
            String name = _currentToken.value;
            nextToken();
            Name nameNode = new Name();
            nameNode.setValue(name);
            return nameNode;
        }
        
        // Parenthesized expression
        if (matchPunctuation("(")) {
            nextToken();
            BaseNode expr = parseExpression();
            consumePunctuation(")");
            return expr;
        }
        
        // Default: empty expression
        return new EmptyExpression();
    }

    /**
     * Parse for statement: for (init; condition; update) { ... }
     */
    private ForLoop parseForStatement() {
        nextToken(); // consume 'for'

        ForLoop forLoop = new ForLoop();
        setNodePosition(forLoop);

        if (consumePunctuation("(")) {
            // Parse init (simplified)
            if (!matchPunctuation(";")) {
                BaseNode init = parseStatement();
                forLoop.setInitializer(init);
            } else {
                nextToken(); // consume ';'
            }

            // Parse condition
            if (!matchPunctuation(";")) {
                BaseNode condition = parseExpression();
                forLoop.setCondition(condition);
            }
            consumePunctuation(";");

            // Parse update
            if (!matchPunctuation(")")) {
                BaseNode update = parseExpression();
                forLoop.setIncrement(update);
            }
            consumePunctuation(")");
        }

        // Parse body
        BaseNode body = parseStatement();
        forLoop.setStatement(body);

        return forLoop;
    }

    /**
     * Parse while statement: while (condition) { ... }
     */
    private WhileLoop parseWhileStatement() {
        nextToken(); // consume 'while'

        WhileLoop whileLoop = new WhileLoop();
        setNodePosition(whileLoop);

        if (consumePunctuation("(")) {
            BaseNode condition = parseExpression();
            whileLoop.setCondition(condition);
            consumePunctuation(")");
        }

        BaseNode body = parseStatement();
        whileLoop.setStatement(body);

        return whileLoop;
    }

    /**
     * Parse try statement: try { ... } catch (e) { ... } finally { ... }
     */
    private TryStatement parseTryStatement() {
        nextToken(); // consume 'try'

        TryStatement tryStmt = new TryStatement();
        setNodePosition(tryStmt);

        // Parse try block
        BaseNode tryBlock = parseBlock();
        tryStmt.setTryBlock(tryBlock);

        // Parse catch clause
        if (matchKeyword("catch")) {
            nextToken();
            CatchClause catchClause = new CatchClause();

            if (consumePunctuation("(")) {
                if (matchToken(TokenType.IDENTIFIER)) {
                    Name param = new Name(_currentToken.value);
                    catchClause.setParam(param);
                    nextToken();
                }
                consumePunctuation(")");
            }

            BaseNode catchBlock = parseBlock();
            catchClause.setBody(catchBlock);
            tryStmt.setCatchClause(catchClause);
        }

        // Parse finally clause
        if (matchKeyword("finally")) {
            nextToken();
            BaseNode finallyBlock = parseBlock();
            tryStmt.setFinallyBlock(finallyBlock);
        }

        return tryStmt;
    }

    /**
     * Parse throw statement: throw expression;
     */
    private ThrowStatement parseThrowStatement() {
        nextToken(); // consume 'throw'

        ThrowStatement throwStmt = new ThrowStatement();
        setNodePosition(throwStmt);

        BaseNode expr = parseExpression();
        throwStmt.setExpression(expr);

        consumePunctuation(";");
        return throwStmt;
    }

    /**
     * Parse break statement: break;
     */
    private BreakStatement parseBreakStatement() {
        nextToken(); // consume 'break'

        BreakStatement breakStmt = new BreakStatement();
        setNodePosition(breakStmt);

        consumePunctuation(";");
        return breakStmt;
    }

    /**
     * Parse continue statement: continue;
     */
    private ContinueStatement parseContinueStatement() {
        nextToken(); // consume 'continue'

        ContinueStatement continueStmt = new ContinueStatement();
        setNodePosition(continueStmt);

        consumePunctuation(";");
        return continueStmt;
    }

    // ========== UTILITY METHODS ==========



    /**
     * Skip whitespace and comments (no-op for token-based parsing)
     */
    private void skipWhitespaceAndComments() {
        // No-op since tokenizer already handles whitespace and comments
    }

    /**
     * Create a simple usage example
     */
    public static void main(String[] args) {
        BasicConverter converter = new BasicConverter();

        String jsCode = "function hello(name) {\n" +
                       "    var greeting = \"Hello, \" + name;\n" +
                       "    return greeting;\n" +
                       "}\n" +
                       "\n" +
                       "var result = hello(\"World\");";

        try {
            RootNode ast = converter.parse(jsCode, null);
            System.out.println("Successfully parsed JavaScript into AST!");
            System.out.println("Root node type: " + ast.getClass().getSimpleName());
            System.out.println("Number of top-level elements: " + ast.getElements().size());
        } catch (Exception e) {
            System.err.println("Parse error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
